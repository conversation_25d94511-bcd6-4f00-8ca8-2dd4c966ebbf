@extends('layouts.app')

@section('title', '2022年国家级教学成果奖申报')
@section('pageType', '1')
@section('pageTitle', '2022年国家级教学成果奖申报')

@section('content')
    <!-- 主轮播图 -->
    <div class="banner">
        <div data-am-widget="slider" class="am-slider am-slider-a2"
             data-am-slider="{'directionNav':false,'slideshowSpeed': 3000}">
            <ul class="am-slides">
                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("index_top_slider"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp
                @forelse($articles as $article)
                    <li>
                        <div class="ddsgg"
                             style="background: url({{user_front_image($article->image)}}) no-repeat center;">
                            <div class="jz">
                                <div class="bn_bt">{{ $article['title'] }}</div>
                            </div>
                        </div>
                    </li>
                @empty
                    <li>
                        <div class="ddsgg"
                             style="background: url({{ asset('static/images/1-200q3113p40-l.jpg') }}) no-repeat center;">
                            <div class="jz">
                                <div class="bn_bt">指向创造力的卓越教师培养模式探索与实践</div>
                            </div>
                        </div>
                    </li>
                @endforelse
            </ul>
        </div>
    </div>

    <!-- 副轮播图 -->
    <div class="sbanner">
        <div data-am-widget="slider" class="am-slider am-slider-b1"
             data-am-slider="{'controlNav':false,'slideshowSpeed': 3000}">
            <ul class="am-slides">
                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("index_top_slider"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp
                @forelse($articles as $article)
                    <li>
                        <div class="ddsgg">
                            <img src="{{user_front_image($article->image)}}" width="100%"
                                 alt="{{ $article['title'] }}"/>
                        </div>
                    </li>
                @empty
                    <li>
                        <div class="ddsgg">
                            <img src="{{ asset('static/picture/1-200q31343370-l.jpg') }}" width="100%"/>
                        </div>
                    </li>
                @endforelse
            </ul>
        </div>
    </div>

    <!-- 成果简介 -->
    <div class="hot">
        <div class="jz">
            <div class="hot_gz l">成果简介</div>
            <div class="hot_news l">
                <a href="{{get_config("index_desc_url")}}" class="h_nr" target="">
                    {{ $introText ?? '创新型人才是创新型国家建设的第一资源，而创新型人才培养必须从基础教育抓起，需要一大批具有创造力的中小学教师。师范大学承担"两代师表"共育重任，是引领教师教育创新发展的策源地，在创新型国家建设和创新型人才培养中具有基础性、先导性、战略性地位。···' }}
                </a>
            </div>
        </div>
    </div>


    @php
        $cate1 = \App\Models\Category::query()->where("id",get_config("article_list_model_1"))->first();
        $cate2 = \App\Models\Category::query()->where("id",get_config("article_list_model_2"))->first();
        $cate3 = \App\Models\Category::query()->where("id",get_config("article_list_model_3"))->first();
        $cate1Articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate1->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
        $cate2Articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate2->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
        $cate3Articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate3->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();

        $sections = [
            ['cate'=>$cate1,'articles'=>$cate1Articles],
            ['cate'=>$cate2,'articles'=>$cate2Articles],
            ['cate'=>$cate3,'articles'=>$cate3Articles]
        ];
    @endphp

        <!-- 标签页内容 -->
    <div class="bg-grey">
        <div class="content">
            <div class="tab-block information-tab">
                <div class="tab-buttons">
                    @foreach($sections as $index=> $section)
                        <h3 class="tab-button @if($index==0) cur @endif " data-tab="{{$index}}">
                            {{ $section['cate']->name }}
                        </h3>
                    @endforeach
                </div>

                <div class="tabs">
                    @foreach($sections as $index => $section)
                        <div class="tab-item {{ $index === 0 ? 'active' : '' }}" id="tab-{{$index}}">
                            <div class="information-tab">
                                <div class="information-left">
                                    <a href="#">
                                        <img src="{{user_front_image($section['cate']->image)}}"
                                             width="500" height="340"
                                             alt="{{ $section['cate']['name'] }}"/>
                                    </a>


                                    <div class="left-bottom">
                                        <div class="article-title">{{ $section['cate']['name'] }}</div>
                                        @if(!empty($section['featured_date']))
                                            <div class="article-time">{{ $section['featured_date'] }}</div>
                                        @endif
                                    </div>
                                </div>

                                <div class="information-right">
                                    @foreach($section['articles'] as $itemIndex => $item)
                                        <div class="article-list {{ $itemIndex === 0 ? 'current' : '' }}">
                                            <a href="{{ $item->getLink($section['cate']) }}" class="article-link"
                                               target="_blank">
                                                <div class="article-head">
                                                    <span class="article-number">{{ $itemIndex + 1 }}</span>
                                                    <span class="article-title">{{ $item['title'] }}</span>
                                                    <span
                                                        class="article-time">{{ \Illuminate\Support\Carbon::parse($item['created_at'])->toDateString() }}</span>
                                                </div>
                                            </a>
                                        </div>
                                    @endforeach

                                    <div class="more-link">
                                        <a href="{{ $section['cate']->getLink() }}" class="btn-more">查看更多</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    {{--    <!-- 主要完成人 -->--}}
    <div class="cont">
        {{--        <div class="c_bkc">--}}
        {{--            <div class="jz">--}}
        {{--                <div class="c_bkebt">--}}
        {{--                    <span class="zwbtz l">主要完成人</span>--}}
        {{--                    <span class="ywfb l">&nbsp;&nbsp;</span>--}}
        {{--                    <a href="{{ route('pages.show', 'zywcr') }}" target="_blank" class="r"></a>--}}
        {{--                </div>--}}

        {{--                <div data-am-widget="tabs" class="am-tabs am-tabs-d2">--}}
        {{--                    <div class="am-tabs-bd nyszz">--}}
        {{--                        @foreach($personGroups ?? [] as $groupIndex => $group)--}}
        {{--                            <div data-tab-panel-{{ $groupIndex }}=""--}}
        {{--                                 class="am-tab-panel mdzznzz {{ $groupIndex === 0 ? 'am-active' : '' }}">--}}
        {{--                                <ul class="ndscc">--}}
        {{--                                    @foreach($group['persons'] ?? [] as $person)--}}
        {{--                                        <li>--}}
        {{--                                            <a href="{{ route('persons.show', $person['slug']) }}">--}}
        {{--                                                <div class="fdyxb">--}}
        {{--                                                    <img src="{{ asset('static/picture/fdj1.png') }}"--}}
        {{--                                                         width="280" class="wydnb" alt=""/>--}}
        {{--                                                    <img src="{{ $person['avatar'] }}"--}}
        {{--                                                         class="fdydb" alt="{{ $person['name'] }}"/>--}}
        {{--                                                </div>--}}
        {{--                                                <span class="gcbt">{{ $person['name'] }}</span>--}}
        {{--                                            </a>--}}
        {{--                                        </li>--}}
        {{--                                    @endforeach--}}
        {{--                                </ul>--}}
        {{--                            </div>--}}
        {{--                        @endforeach--}}
        {{--                    </div>--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </div>--}}

        <!-- 其他内容区域 -->
        <div class="jz">
            <div class="c_bkb">
                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_4"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp
                    <!-- 行业影响 -->
                <div class="jsjl" style="margin-top: 0">
                    <div class="jsllbt">
                        <span class="zwbtz l">{{$cate->name}}</span>
                        <a href="{{ $cate->getLink() }}" target="_blank" class="ywfr r"></a>
                    </div>
                    <ul class="jsllb">
                        @foreach($articles ?? [] as $article)
                            <li>
                                <a href="{{ $article->getLink($cate) }}" class="l cdwzzx">{{ $article['title'] }}</a>
                                <span
                                    class="r">{{ \Illuminate\Support\Carbon::parse($item['created_at'])->toDateString() }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>

                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_5"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp
                    <!-- 行业影响 -->
                <div class="jsjl" style="margin-top: 0">
                    <div class="jsllbt">
                        <span class="zwbtz l">{{$cate->name}}</span>
                        <span class="ywfb l"></span>
                        <a href="{{ $cate->getLink() }}" target="_blank" class="ywfr r"></a>
                    </div>
                    <ul class="jsllb">
                        @foreach($articles ?? [] as $article)
                            <li>
                                <a href="{{ $article->getLink($cate) }}" class="l cdwzzx">{{ $article['title'] }}</a>
                                <span
                                    class="r">{{ \Illuminate\Support\Carbon::parse($item['created_at'])->toDateString() }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>

                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_6"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp


                    <!-- 研究成果 -->
                <div class="c_xxzn r">
                    <div class="whfwbt">
                        <span class="zwbtz l">{{$cate->name}}</span>
                        <a href="{{ $cate->getLink() }}" target="_blank" class="ywfr r"></a>
                    </div>
                    <ul class="dbxgn jkzx">
                        @foreach($articles ?? [] as $article)
                            <li style="margin-right: 0">
                                <a href="{{ $article->getLink($cate) }}" target="_blank">
                                    <img src="{{user_front_image($article->image)}}"
                                         alt="{{ $article['title'] }}"/><br/>
                                    {{ $article['title'] }}
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>

            </div>
        </div>


        {{--        <!-- 更多内容区域 -->--}}
        <div class="c_bkd">
            <div class="jz">
                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_7"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp
                @if($cate)

                    <div class="jsjl">
                        <div class="jsllbt">
                            <span class="zwbtz l">{{$cate->name}}</span>
                            <span class="ywfb l"></span>
                            <a href="{{ $cate->getLink() }}" target="_blank" class="ywfr r"></a>
                        </div>
                        <img src="{{ user_front_image($cate->image) }}"/>
                        <ul class="jsllb">
                            @foreach($articles ?? [] as $article)
                                <li>
                                    <a href="{{ $article->getLink($cate) }}"
                                       class="l cdwzzx">{{ $article['title'] }}</a>
                                    <span
                                        class="r">{{ \Illuminate\Support\Carbon::parse($item['created_at'])->toDateString() }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_8"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp

                <div class="jsjl">
                    <div class="jsllbt">
                        <span class="zwbtz l">{{$cate->name}}</span>
                        <span class="ywfb l"></span>
                        <a href="{{ $cate->getLink() }}" target="_blank" class="ywfr r"></a>
                    </div>
                    <img src="{{ user_front_image($cate->image) }}"/>
                    <ul class="jsllb">
                        @foreach($articles ?? [] as $article)
                            <li>
                                <a href="{{ $article->getLink($cate) }}" class="l cdwzzx">{{ $article['title'] }}</a>
                                <span
                                    class="r">{{ \Illuminate\Support\Carbon::parse($item['created_at'])->toDateString() }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>

                @php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_9"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                @endphp

                <div class="whfw">
                    <div class="whfwbt">
                        <span class="zwbtz l">{{$cate->name}}</span>
                        <span class="ywfb l"></span>
                        <a href="{{ $cate->getLink() }}" target="_blank" class="ywfr r"></a>
                    </div>
                    <img src="{{ user_front_image($cate->image) }}"/>
                    <ul class="jsllb">
                        @foreach($articles ?? [] as $article)
                            <li>
                                <a href="{{ $article->getLink($cate) }}" class="l cdwzzx">{{ $article['title'] }}</a>
                                <span
                                    class="r">{{ \Illuminate\Support\Carbon::parse($item['created_at'])->toDateString() }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>

            </div>
        </div>


        @php
            $cate = \App\Models\Category::query()->where("id",get_config("bottom_link"))->first();
            $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
        @endphp
        @if($cate)
            <div class="c_bke c_kxkx">
                <div class="jz">
                    <div class="c_bkebt">
                        <span class="zwbtz l">{{$cate->name}}</span>
                        <span class="ywfb l">&nbsp;/&nbsp;links</span>
                    </div>
                    <ul class="lsjl">
                        @foreach($articles ?? [] as $article)
                            <li>
                                <a href="{{ $article->getLink($cate) }}" target="_blank">
                                    <img src="{{ user_front_image($article->image) }}"
                                         width="283" height="75" border="0"
                                         alt="{{ $article['title'] }}"/>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('scripts')
    <script type="text/javascript">
        // Tab切换
        $(".tab-button").click(function () {
            var tab = $(this).data("tab");
            $(this).addClass("cur").siblings(".tab-button").removeClass("cur");
            $("#tab-" + tab + "").addClass("active").siblings(".tab-item").removeClass("active");
        });

        // 新闻列表切换
        $(".information-tab .article-list").hover(
            function () {
                $(this).addClass("current").siblings(".article-list").removeClass("current");
            }
        );
    </script>
@endpush

@push('scripts')
    <script>
        // 标签页切换
        $(".tab-button").click(function () {
            var tab = $(this).data("tab")
            $(this).addClass("cur").siblings(".tab-button").removeClass("cur")
            $("#tab-" + tab + "")
                .addClass("active")
                .siblings(".tab-item")
                .removeClass("active")
        })

        // 新闻列表切换
        $(".information-tab .article-list").hover(
            function () {
                $(this)
                    .addClass("current")
                    .siblings(".article-list")
                    .removeClass("current")
            },
            function () {
                $(this)
                    .parent(".information-right")
                    .find(".article-list:first-of-type")
                    .addClass("current")
                    .siblings(".article-list")
                    .removeClass("current")
            }
        )
    </script>
@endpush
