<?php

namespace Database\Factories;

use App\Models\ClientUser;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClientUserFactory extends Factory
{
    protected $model = ClientUser::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'open_id'       => $this->faker->text(20),
            'nick_name'     => $this->faker->unique()->name(),
            'gender'        => $this->faker->randomElement([1, 2, 0]),
            'avatar_url'    => "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
            'phone'         => $this->faker->phoneNumber(),
            'balance_money' => 0,
            'status'        => "normal",
        ];
    }
}
