<?php


namespace App\Admin\Extensions\Renderable;


use App\Admin\Repositories\Institution;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class InstitutionTable extends LazyRenderable
{

    public function grid(): Grid
    {

        return Grid::make(new Institution(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('name');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->paginate(10);
            $grid->disableActions();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('name')->width(4);
            });
        });
    }
}
