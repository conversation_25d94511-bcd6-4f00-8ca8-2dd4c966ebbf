<?php

namespace App\Admin\Metrics\Examples;

use App\Models\Article;
use Dcat\Admin\Widgets\Metrics\Card;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;

class TotalArticle extends Card
{
    /**
     * 卡片底部内容.
     *
     * @var string|Renderable|\Closure
     */
    protected $footer;

    /**
     * 初始化卡片.
     */
    protected function init()
    {
        parent::init();

        $this->title('文章数量');
        $this->dropdown([
            'all' => '所有',
            '7'   => '最近7天',
            '15'  => '最近半月',
            '30'  => '最近一月',
            '365' => '最近一年',
        ]);
        $this->height(0);
    }

    /**
     * 处理请求.
     *
     * @param Request $request
     *
     * @return void
     */
    public function handle(Request $request)
    {
        $this->content(
            $this->getStatistics(
                $request->input('option', 'all')
            )['value']
        );
    }

    /**
     * 渲染卡片内容.
     *
     * @return string
     */
    public function renderContent()
    {
        $content = parent::renderContent();

        return <<<HTML
<div class="d-flex justify-content-between align-items-center mt-1">
    <h2 class="ml-1 font-lg-1">{$content}</h2>
</div>
HTML;
    }

    private function getStatistics($days)
    {
        $res = [
            "value" => 0
        ];

        $day_end = now()->endOfDay();
        $builder = Article::query();
        if ($days !== "all") {
            $day_start = now()->subDays($days)->startOfDay();
            $builder->where("created_at", ">=", $day_start);
        }
        $res['value'] = $builder->where("created_at", "<=", $day_end)->count();
        return $res;
    }
}
