<!DOCTYPE html>
<html class="no-js">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><?php echo $__env->yieldContent('title', '2022年国家级教学成果奖申报'); ?></title>
    <meta name="pageType" content="<?php echo $__env->yieldContent('pageType', '1'); ?>" />
    <meta name="pageTitle" content="<?php echo $__env->yieldContent('pageTitle', '2022年国家级教学成果奖申报'); ?>" />
    <meta name="keywords" content="<?php echo $__env->yieldContent('keywords', '2022年国家级教学成果奖申报'); ?>" />
    <meta name="description" content="<?php echo $__env->yieldContent('description', '东北师范大学2022年申报国家级教学成果奖成果项目'); ?>" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('static/css/amazeui.min.css'), false); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('static/css/app.css'), false); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('static/css/index.css'), false); ?>" />
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('static/css/style.css'), false); ?>" />
    
    <?php echo $__env->yieldPushContent('styles'); ?>
    
    <!--[if (gte IE 9)|!(IE)]><!-->
    <script src="<?php echo e(asset('static/js/jquery.min.js'), false); ?>"></script>
    <script src="<?php echo e(asset('static/js/amazeui.min.js'), false); ?>"></script>
    <!--<![endif]-->
</head>
<body>
    <!-- 头部区域（包含顶部信息栏、Logo、导航） -->
    <?php echo $__env->make('components.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    
    <!-- 页面内容 -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>
    
    <!-- 页脚 -->
    <?php echo $__env->make('components.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    
    <!-- JavaScript -->
    <script type="text/JavaScript" src="<?php echo e(asset('static/js/navcal.js'), false); ?>"></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH /mnt/c/Users/<USER>/code/jiaoxuechengguojiang/20250905-jxcg-theme3/resources/views/layouts/app.blade.php ENDPATH**/ ?>