<?php


namespace App\Admin\Renderable;


use App\Admin\Repositories\Category;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class CategoryTable extends LazyRenderable
{

    public function grid(): Grid
    {
        $id = $this->id;

        return Grid::make(new Category(), function (Grid $grid) {
            $grid->column('id');
//            $grid->level->tree();
            $grid->column('name');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->quickSearch(['id', 'name']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name')->width(4);
            });
        });
    }
}
