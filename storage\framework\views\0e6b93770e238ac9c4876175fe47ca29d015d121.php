

<?php $__env->startSection('title', '2022年国家级教学成果奖申报'); ?>
<?php $__env->startSection('pageType', '1'); ?>
<?php $__env->startSection('pageTitle', '2022年国家级教学成果奖申报'); ?>

<?php $__env->startSection('content'); ?>
    <!-- 主轮播图 -->
    <div class="banner">
        <div data-am-widget="slider" class="am-slider am-slider-a2"
             data-am-slider="{'directionNav':false,'slideshowSpeed': 3000}">
            <ul class="am-slides">
                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("index_top_slider"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>
                <?php $__empty_1 = true; $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li>
                        <div class="ddsgg"
                             style="background: url(<?php echo e(user_front_image($article->image), false); ?>) no-repeat center;">
                            <div class="jz">
                                <div class="bn_bt"><?php echo e($article['title'], false); ?></div>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li>
                        <div class="ddsgg"
                             style="background: url(<?php echo e(asset('static/images/1-200q3113p40-l.jpg'), false); ?>) no-repeat center;">
                            <div class="jz">
                                <div class="bn_bt">指向创造力的卓越教师培养模式探索与实践</div>
                            </div>
                        </div>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

    <!-- 副轮播图 -->
    <div class="sbanner">
        <div data-am-widget="slider" class="am-slider am-slider-b1"
             data-am-slider="{'controlNav':false,'slideshowSpeed': 3000}">
            <ul class="am-slides">
                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("index_top_slider"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>
                <?php $__empty_1 = true; $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li>
                        <div class="ddsgg">
                            <img src="<?php echo e(user_front_image($article->image), false); ?>" width="100%"
                                 alt="<?php echo e($article['title'], false); ?>"/>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li>
                        <div class="ddsgg">
                            <img src="<?php echo e(asset('static/picture/1-200q31343370-l.jpg'), false); ?>" width="100%"/>
                        </div>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

    <!-- 成果简介 -->
    <div class="hot">
        <div class="jz">
            <div class="hot_gz l">成果简介</div>
            <div class="hot_news l">
                <a href="<?php echo e(get_config("index_desc_url"), false); ?>" class="h_nr" target="">
                    <?php echo e($introText ?? '创新型人才是创新型国家建设的第一资源，而创新型人才培养必须从基础教育抓起，需要一大批具有创造力的中小学教师。师范大学承担"两代师表"共育重任，是引领教师教育创新发展的策源地，在创新型国家建设和创新型人才培养中具有基础性、先导性、战略性地位。···', false); ?>

                </a>
            </div>
        </div>
    </div>


    <?php
        $cate1 = \App\Models\Category::query()->where("id",get_config("article_list_model_1"))->first();
        $cate2 = \App\Models\Category::query()->where("id",get_config("article_list_model_2"))->first();
        $cate3 = \App\Models\Category::query()->where("id",get_config("article_list_model_3"))->first();
        $cate1Articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate1->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
        $cate2Articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate2->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
        $cate3Articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate3->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();

        $sections = [
            ['cate'=>$cate1,'articles'=>$cate1Articles],
            ['cate'=>$cate2,'articles'=>$cate2Articles],
            ['cate'=>$cate3,'articles'=>$cate3Articles]
        ];
    ?>

        <!-- 标签页内容 -->
    <div class="bg-grey">
        <div class="content">
            <div class="tab-block information-tab">
                <div class="tab-buttons">
                    <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index=> $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <h3 class="tab-button <?php if($index==0): ?> cur <?php endif; ?> " data-tab="<?php echo e($index, false); ?>">
                            <?php echo e($section['cate']->name, false); ?>

                        </h3>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div class="tabs">
                    <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="tab-item <?php echo e($index === 0 ? 'active' : '', false); ?>" id="tab-<?php echo e($index, false); ?>">
                            <div class="information-tab">
                                <div class="information-left">
                                    <a href="#">
                                        <img src="<?php echo e(user_front_image($section['cate']->image), false); ?>"
                                             width="500" height="340"
                                             alt="<?php echo e($section['cate']['name'], false); ?>"/>
                                    </a>


                                    <div class="left-bottom">
                                        <div class="article-title"><?php echo e($section['cate']['name'], false); ?></div>
                                        <?php if(!empty($section['featured_date'])): ?>
                                            <div class="article-time"><?php echo e($section['featured_date'], false); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="information-right">
                                    <?php $__currentLoopData = $section['articles']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itemIndex => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="article-list <?php echo e($itemIndex === 0 ? 'current' : '', false); ?>">
                                            <a href="<?php echo e($item->getLink($section['cate']), false); ?>" class="article-link"
                                               target="_blank">
                                                <div class="article-head">
                                                    <span class="article-number"><?php echo e($itemIndex + 1, false); ?></span>
                                                    <span class="article-title"><?php echo e($item['title'], false); ?></span>
                                                    <span
                                                        class="article-time"><?php echo e(\Illuminate\Support\Carbon::parse($item['created_at'])->toDateString(), false); ?></span>
                                                </div>
                                            </a>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    <div class="more-link">
                                        <a href="<?php echo e($section['cate']->getLink(), false); ?>" class="btn-more">查看更多</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

    
    <div class="cont">
        
        
        
        
        
        
        

        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        

        <!-- 其他内容区域 -->
        <div class="jz">
            <div class="c_bkb">
                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_4"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>
                    <!-- 行业影响 -->
                <div class="jsjl" style="margin-top: 0">
                    <div class="jsllbt">
                        <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                        <a href="<?php echo e($cate->getLink(), false); ?>" target="_blank" class="ywfr r"></a>
                    </div>
                    <ul class="jsllb">
                        <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e($article->getLink($cate), false); ?>" class="l cdwzzx"><?php echo e($article['title'], false); ?></a>
                                <span
                                    class="r"><?php echo e(\Illuminate\Support\Carbon::parse($item['created_at'])->toDateString(), false); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_5"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>
                    <!-- 行业影响 -->
                <div class="jsjl" style="margin-top: 0">
                    <div class="jsllbt">
                        <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                        <span class="ywfb l"></span>
                        <a href="<?php echo e($cate->getLink(), false); ?>" target="_blank" class="ywfr r"></a>
                    </div>
                    <ul class="jsllb">
                        <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e($article->getLink($cate), false); ?>" class="l cdwzzx"><?php echo e($article['title'], false); ?></a>
                                <span
                                    class="r"><?php echo e(\Illuminate\Support\Carbon::parse($item['created_at'])->toDateString(), false); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_6"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>


                    <!-- 研究成果 -->
                <div class="c_xxzn r">
                    <div class="whfwbt">
                        <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                        <a href="<?php echo e($cate->getLink(), false); ?>" target="_blank" class="ywfr r"></a>
                    </div>
                    <ul class="dbxgn jkzx">
                        <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li style="margin-right: 0">
                                <a href="<?php echo e($article->getLink($cate), false); ?>" target="_blank">
                                    <img src="<?php echo e(user_front_image($article->image), false); ?>"
                                         alt="<?php echo e($article['title'], false); ?>"/><br/>
                                    <?php echo e($article['title'], false); ?>

                                </a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

            </div>
        </div>


        
        <div class="c_bkd">
            <div class="jz">
                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_7"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>
                <?php if($cate): ?>

                    <div class="jsjl">
                        <div class="jsllbt">
                            <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                            <span class="ywfb l"></span>
                            <a href="<?php echo e($cate->getLink(), false); ?>" target="_blank" class="ywfr r"></a>
                        </div>
                        <img src="<?php echo e(user_front_image($cate->image), false); ?>"/>
                        <ul class="jsllb">
                            <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="<?php echo e($article->getLink($cate), false); ?>"
                                       class="l cdwzzx"><?php echo e($article['title'], false); ?></a>
                                    <span
                                        class="r"><?php echo e(\Illuminate\Support\Carbon::parse($item['created_at'])->toDateString(), false); ?></span>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_8"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>

                <div class="jsjl">
                    <div class="jsllbt">
                        <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                        <span class="ywfb l"></span>
                        <a href="<?php echo e($cate->getLink(), false); ?>" target="_blank" class="ywfr r"></a>
                    </div>
                    <img src="<?php echo e(user_front_image($cate->image), false); ?>"/>
                    <ul class="jsllb">
                        <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e($article->getLink($cate), false); ?>" class="l cdwzzx"><?php echo e($article['title'], false); ?></a>
                                <span
                                    class="r"><?php echo e(\Illuminate\Support\Carbon::parse($item['created_at'])->toDateString(), false); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <?php
                    $cate = \App\Models\Category::query()->where("id",get_config("article_list_model_9"))->first();
                    $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
                ?>

                <div class="whfw">
                    <div class="whfwbt">
                        <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                        <span class="ywfb l"></span>
                        <a href="<?php echo e($cate->getLink(), false); ?>" target="_blank" class="ywfr r"></a>
                    </div>
                    <img src="<?php echo e(user_front_image($cate->image), false); ?>"/>
                    <ul class="jsllb">
                        <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e($article->getLink($cate), false); ?>" class="l cdwzzx"><?php echo e($article['title'], false); ?></a>
                                <span
                                    class="r"><?php echo e(\Illuminate\Support\Carbon::parse($item['created_at'])->toDateString(), false); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

            </div>
        </div>


        <?php
            $cate = \App\Models\Category::query()->where("id",get_config("bottom_link"))->first();
            $articles = \App\Models\Article::query()->whereIn('id',get_cat_all_article_ids($cate->id ?? null))->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(5)->get();
        ?>
        <?php if($cate): ?>
            <div class="c_bke c_kxkx">
                <div class="jz">
                    <div class="c_bkebt">
                        <span class="zwbtz l"><?php echo e($cate->name, false); ?></span>
                        <span class="ywfb l">&nbsp;/&nbsp;links</span>
                    </div>
                    <ul class="lsjl">
                        <?php $__currentLoopData = $articles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e($article->getLink($cate), false); ?>" target="_blank">
                                    <img src="<?php echo e(user_front_image($article->image), false); ?>"
                                         width="283" height="75" border="0"
                                         alt="<?php echo e($article['title'], false); ?>"/>
                                </a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        // Tab切换
        $(".tab-button").click(function () {
            var tab = $(this).data("tab");
            $(this).addClass("cur").siblings(".tab-button").removeClass("cur");
            $("#tab-" + tab + "").addClass("active").siblings(".tab-item").removeClass("active");
        });

        // 新闻列表切换
        $(".information-tab .article-list").hover(
            function () {
                $(this).addClass("current").siblings(".article-list").removeClass("current");
            }
        );
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // 标签页切换
        $(".tab-button").click(function () {
            var tab = $(this).data("tab")
            $(this).addClass("cur").siblings(".tab-button").removeClass("cur")
            $("#tab-" + tab + "")
                .addClass("active")
                .siblings(".tab-item")
                .removeClass("active")
        })

        // 新闻列表切换
        $(".information-tab .article-list").hover(
            function () {
                $(this)
                    .addClass("current")
                    .siblings(".article-list")
                    .removeClass("current")
            },
            function () {
                $(this)
                    .parent(".information-right")
                    .find(".article-list:first-of-type")
                    .addClass("current")
                    .siblings(".article-list")
                    .removeClass("current")
            }
        )
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/c/Users/<USER>/code/jiaoxuechengguojiang/20250905-jxcg-theme3/resources/views/pages/home.blade.php ENDPATH**/ ?>