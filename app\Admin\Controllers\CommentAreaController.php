<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\ClientUserTable;
use App\Admin\Repositories\CommentArea;
use App\Models\ClientUser;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CommentAreaController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CommentArea(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column("user")->display(function ($user) {
                return $user->nick_name ?? "";
            });
            $grid->column('title');
            $grid->column('abstract');
            $grid->column('wpa_name');
            $grid->column('comment_audit')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->model()->orderByDesc("id");
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal("id", "用户")->selectTable(ClientUserTable::make())->model(ClientUser::class, "id", "nick_name");
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CommentArea(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('title');
            $show->field('abstract');
            $show->field('wpa_name');
            $show->field('comment_audit')->using([
                0 => "无需审核",
                1 => "需要审核",
            ]);
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CommentArea(), function (Form $form) {
            $form->display('id');
            $form->selectTable("user_id", '用户')->title("用户")->from(ClientUserTable::make())->model(ClientUser::class, 'id', 'nick_name')->required();

            $form->text('title');
            $form->textarea('abstract');
            $form->text('wpa_name');
            $form->switch('comment_audit');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
