<?php


namespace App\Admin\Renderable;


use App\Admin\Repositories\Article;
use App\Models\ClientUser;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class ClientUserTable extends LazyRenderable
{

    public function grid(): Grid
    {
        $id = $this->id;

        return Grid::make(new ClientUser(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('nick_name', "用户昵称");
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->quickSearch(['id', 'nick_name']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('nick_name')->width(4);
            });
        });
    }
}
