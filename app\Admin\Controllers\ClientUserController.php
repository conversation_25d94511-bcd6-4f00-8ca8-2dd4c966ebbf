<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ClientUser;
use App\Admin\Status\ClientUserStatus;
use App\Models\Comment;
use App\Models\CommentArea;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ClientUserController extends AdminController
{
    private $genderMap = [
        0 => '未知',
        1 => '男',
        2 => '女',
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ClientUser(), function (Grid $grid) {
            $grid->model()->orderby('created_at', 'desc');
            $grid->column('id')->sortable();
            $grid->column('nick_name');
            $grid->column('gender')->using($this->genderMap);
            $grid->column('avatar_url')->image('', 50, 50);
            $grid->column('phone');
            $grid->column('balance_money');
            $grid->column('status')->radio(ClientUserStatus::getMap());
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->disableDeleteButton();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('nick_name');
                $filter->like('phone');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ClientUser(), function (Show $show) {
            $show->field('id');
            $show->field('union_id');
            $show->field('open_id');
            $show->field('nick_name');
            $show->field('gender');
            $show->field('avatar_url')->image('', 50, 50);
            $show->field('phone');
            $show->field('status')->using(ClientUserStatus::getMap());
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ClientUser(), function (Form $form) {
            $form->radio('gender')->options($this->genderMap);
            $form->image('avatar_url')->disable();
            $form->text('phone');
            $form->radio('status')->options(ClientUserStatus::getMap());

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
