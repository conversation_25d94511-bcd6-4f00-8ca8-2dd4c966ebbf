<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');

    /**
     * 内容管理相关
     */
    $router->group([
        'prefix' => 'content'
    ], function () use ($router) {
        $router->resource('categories', 'CategoryController');
        $router->resource('articles', 'ArticleController');
    });

    /**
     * 菜单管理
     */
    $router->resource('navs', 'NavController');

    /**
     * 网站设置
     */
    $router->group([
        'prefix' => 'setting'
    ], function ($router) {
        $router->get('front', 'SettingController@front')->name("setting.front");
        $router->get('admin', 'SettingController@admin')->name("setting.admin");
        $router->get('clear', 'SettingController@clear')->name("setting.clear");
    });
});
