<?php

namespace App\Admin\Controllers;

use App\Models\Article;
use App\Models\Category;
use App\Models\Nav;
use Dcat\Admin\Form;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Tree;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Widgets\Form as WidgetForm;

class NavController extends AdminController
{
    private $categories = [];

    public function __construct()
    {
        $this->categories = [];

        $options0["#"] = "无";
        $options0["/"] = "首页";
        $options1      = array_merge([], Category::query()->pluck('name', 'alias')->toArray());
        $options2      = array_merge([], Article::query()->where("alias", "!=", null)->pluck('title', 'alias')->toArray());

        $this->categories[] = [
            "label"   => '默认',
            "options" => $options0
        ];


        $this->categories[] = [
            "label"   => '分类',
            "options" => $options1
        ];

//
//        $this->categories[] = [
//            "label"   => '文章',
//            "options" => $options2
//        ];
    }

    public function title()
    {
        return trans('admin.menu');
    }

    public function index(Content $content)
    {
        return $content
            ->title($this->title())
            ->description(trans('admin.list'))
            ->body(function (Row $row) {
                $row->column(7, $this->treeView()->render());
                $row->column(5, function (Column $column) {
                    $form = new WidgetForm();
                    $form->action(admin_url('navs'));
                    $form->select('parent_id', trans('admin.parent_id'))->options(Nav::selectOptions());
                    $form->text('title', trans('admin.title'))->required();
                    $form->select('uri', trans('admin.uri'))->groups(
                        $this->categories
                    )->required();

                    $form->select('target', "新标签打开")->options([
                        "0" => "否",
                        "1" => "是",
                    ])->default("0");

                    $form->width(9, 2);
                    $column->append(Box::make(trans('admin.new'), $form));
                });
            });
    }

    /**
     * @return \Dcat\Admin\Tree
     */
    protected function treeView()
    {

        return new Tree(new \App\Models\Nav(), function (Tree $tree) {
            $tree->disableCreateButton();
            $tree->disableQuickCreateButton();
            $tree->disableEditButton();
            $tree->maxDepth(3);

            $tree->actions(function (Tree\Actions $actions) {
                if ($actions->getRow()->extension) {
                    $actions->disableDelete();
                }
            });

            $tree->branch(function ($branch) {
                $payload = "<i class='fa {$branch['icon']}'></i>&nbsp;<strong>{$branch['title']}</strong>";

                if (!isset($branch['children'])) {
                    if (url()->isValidUrl($branch['uri'])) {
                        $uri = $branch['uri'];
                    } else {
                        $uri = $branch['uri'];
                    }
                    $payload .= "&nbsp;&nbsp;&nbsp;<a href=\"$uri\" class=\"dd-nodrag\">$uri</a>";
                }
                return $payload;
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    public function form()
    {
        return Form::make(Nav::query(), function (Form $form) {
            $form->tools(function (Form\Tools $tools) {
                $tools->disableView();
            });
            $form->display('id', 'ID');
            $form->select('parent_id', trans('admin.parent_id'))->options(function () {
                return Nav::selectOptions();
            })->saving(function ($v) {
                return (int)$v;
            });
            $form->text('title', trans('admin.title'))->required();
            $form->select('uri', trans('admin.uri'))->groups($this->categories)->required();
            $form->switch('show', trans('admin.show'));
            $form->select('target', "新标签打开")->options([
                "0" => "否",
                "1" => "是",
            ])->default("0");
            $form->display('created_at', trans('admin.created_at'));
            $form->display('updated_at', trans('admin.updated_at'));
        })->saved(function (Form $form, $result) {
            $response = $form->response()->location('navs');

            if ($result) {
                return $response->success(__('admin.save_succeeded'));
            }

            return $response->info(__('admin.nothing_updated'));
        });
    }

}
