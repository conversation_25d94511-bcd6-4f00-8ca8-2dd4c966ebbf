<?php

namespace App\Http\Middleware;

use App\Exceptions\ValidateFailException;
use Closure;
use Illuminate\Http\Request;

class CheckClientUserStatus
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth("api")->user()->status == \App\Admin\Status\ClientUserStatus::disable) {
            throw new ValidateFailException("账号已被禁用！");
        }
        return $next($request);
    }
}
