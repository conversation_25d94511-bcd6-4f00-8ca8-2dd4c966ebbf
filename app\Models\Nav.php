<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Dcat\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Nav extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes,
        ModelTree {
        allNodes as treeAllNodes;
        ModelTree::boot as treeBoot;
    }

    public function child()
    {
        return $this->hasMany(Nav::class, "parent_id", "id");
    }

    public function parent()
    {
        return $this->belongsTo(Nav::class, "parent_id", "id");
    }
}
