<!-- 顶部信息栏 -->
<div class="top">
    <div class="jz">
        <div class="tl l">
            欢迎进入东北师范大学2022年申报国家级教学成果奖成果项目网站！
        </div>
        <div class="tr r" id="clock"></div>
    </div>
</div>

<!-- 头部Logo -->
<div class="head">
    <div class="jz">
        <a href="" class="l p_sj">
            <img src="<?php echo e(asset('static/picture/1638613778980174.png'), false); ?>"
                 alt="东北师范大学2022年申报国家级教学成果奖成果项目" />
        </a>
    </div>
</div>

<!-- 主导航 -->
<div class="nav">
    <div class="jz">
        <ul class="nav_main">
            <?php $__currentLoopData = $menuItems ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="yiji_li">
                    <?php if($item['type'] == 'external'): ?>
                        <a class="wh_wbd" href="<?php echo e($item['url'], false); ?>" target="_blank">
                            <?php echo e($item['title'], false); ?>

                        </a>
                    <?php else: ?>
                        <a class="wh_wbd" href="<?php echo e($item['url'], false); ?>">
                            <?php echo e($item['title'], false); ?>

                        </a>
                    <?php endif; ?>

                    <?php if(isset($item['children']) && count($item['children']) > 0): ?>
                        <ul class="nav_c">
                            <?php $__currentLoopData = $item['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="erji_li c">
                                    <a href="<?php echo e($child['url'], false); ?>"><?php echo e($child['title'], false); ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    <?php endif; ?>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
</div>

<!-- 移动端导航 -->
<nav data-am-widget="menu" class="am-menu am-menu-dropdown1" data-am-menu-collapse="">
    <a href="javascript: void(0)" class="am-menu-toggle">
        <img src="<?php echo e(asset('static/picture/ddh.png'), false); ?>" alt="Menu Toggle" />
    </a>
    <ul class="am-menu-nav am-avg-sm-1 am-collapse">
        <?php $__currentLoopData = $menuItems ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="am-parent">
                <?php if($item['type'] == 'external'): ?>
                    <a href="<?php echo e($item['url'], false); ?>" target="_blank"><?php echo e($item['title'], false); ?></a>
                <?php else: ?>
                    <a href="<?php echo e($item['url'], false); ?>"><?php echo e($item['title'], false); ?></a>
                <?php endif; ?>

                <?php if(isset($item['children']) && count($item['children']) > 0): ?>
                    <ul class="am-menu-sub am-collapse am-avg-sm-2">
                        <?php $__currentLoopData = $item['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="erji_li c">
                                <a href="<?php echo e($child['url'], false); ?>"><?php echo e($child['title'], false); ?></a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                <?php endif; ?>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
</nav>
<?php /**PATH /mnt/c/Users/<USER>/code/jiaoxuechengguojiang/20250905-jxcg-theme3/resources/views/components/header.blade.php ENDPATH**/ ?>