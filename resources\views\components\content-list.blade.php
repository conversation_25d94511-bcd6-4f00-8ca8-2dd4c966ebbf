@props(['items' => [], 'type' => 'default'])

@if($type === 'file-list')
<!-- 文件列表样式 -->
<div class="wjzlb">
    @foreach($items as $item)
        <div class="wjzlb-item">
            <a href="{{ $item['url'] }}" target="{{ $item['target'] ?? '_blank' }}" class="wjzlb-link">
                @if(!empty($item['icon']))
                    <img src="{{ asset($item['icon']) }}" alt="" class="wjzlb-icon" />
                @endif
                <span class="wjzlb-title">{{ $item['title'] }}</span>
                @if(!empty($item['date']))
                    <span class="wjzlb-date">{{ $item['date'] }}</span>
                @endif
            </a>
        </div>
    @endforeach
</div>
@elseif($type === 'numbered-list')
<!-- 编号列表样式 -->
<ol class="num">
    @foreach($items as $item)
        <li>
            @if(!empty($item['url']))
                <a href="{{ $item['url'] }}" target="{{ $item['target'] ?? '_blank' }}">
                    {{ $item['title'] }}
                </a>
            @else
                {{ $item['title'] }}
            @endif
            @if(!empty($item['date']))
                <span class="date">（{{ $item['date'] }}）</span>
            @endif
        </li>
    @endforeach
</ol>
@else
<!-- 默认列表样式 -->
<ul class="content-list">
    @foreach($items as $item)
        <li class="content-item">
            @if(!empty($item['url']))
                <a href="{{ $item['url'] }}" target="{{ $item['target'] ?? '_blank' }}" class="content-link">
                    <div class="content-title">{{ $item['title'] }}</div>
                    @if(!empty($item['description']))
                        <div class="content-description">{{ $item['description'] }}</div>
                    @endif
                    @if(!empty($item['date']))
                        <div class="content-date">{{ $item['date'] }}</div>
                    @endif
                </a>
            @else
                <div class="content-title">{{ $item['title'] }}</div>
                @if(!empty($item['description']))
                    <div class="content-description">{{ $item['description'] }}</div>
                @endif
                @if(!empty($item['date']))
                    <div class="content-date">{{ $item['date'] }}</div>
                @endif
            @endif
        </li>
    @endforeach
</ul>
@endif
