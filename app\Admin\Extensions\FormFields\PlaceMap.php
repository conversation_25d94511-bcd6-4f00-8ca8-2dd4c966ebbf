<?php


namespace App\Admin\Extensions\FormFields;


use Dcat\Admin\Admin;
use Dcat\Admin\Form\Field;

class PlaceMap extends Field
{
    protected $column = [];

    private $height = "300px";

    protected $view = "admin.extensions.form-fields.place-map";

    public function __construct($column, $arguments = [])
    {
        $this->column['lat'] = (string)$column;
        $this->column['lon'] = (string)$arguments[0];
        $this->column['address'] = (string)$arguments[1];

        array_shift($arguments);
        array_shift($arguments);
        $this->label = $this->formatLabel($arguments);
    }

    public static function requireAssets()
    {
        Admin::js("https://map.qq.com/api/gljs?v=2.exp&key=2RLBZ-L5PKF-23RJB-N7ZW6-LULUE-7BFIV");
        Admin::js("https://map.qq.com/api/js?v=2.exp&key=2RLBZ-L5PKF-23RJB-N7ZW6-LULUE-7BFIV");
    }

    public function height(string $height)
    {
        $this->height = $height;

        return $this;
    }

    protected function getDefaultElementClass()
    {
        $class = $this->normalizeElementClass($this->column['lat']) . $this->normalizeElementClass($this->column['lon']);

        return [$class, static::NORMAL_CLASS];
    }

    public function render()
    {
        $this->addVariables(['height' => $this->height]);

        return parent::render();
    }
}
