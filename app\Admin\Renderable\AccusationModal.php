<?php


namespace App\Admin\Renderable;

use App\Admin\Status\AccusationStatus;
use App\Models\Accusation;
use App\Models\Comment;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Contracts\LazyRenderable;
use Illuminate\Support\Facades\DB;

class AccusationModal extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        //接收弹窗提交过来的数据，进行处理
//        dd($input);


        DB::beginTransaction();
        try {
            $id = $input['id'];

            $accusation = Accusation::query()->with([
                'user',
                'comment'
            ])->where("id", $id)->first();
            if ($input['flag'] == 1) {
                $accusation->update([
                    "status" => AccusationStatus::handled
                ]);
                Comment::query()->where("id", $accusation->comment_id)->delete();
            } else {
                $accusation->update([
                    "status" => AccusationStatus::rejected
                ]);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->response()->error($exception->getMessage());
        }
        return $this->response()->success($input['flag'] == 1 ? '处理成功' : "驳回成功")->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $id         = $this->payload['key'];
        $accusation = Accusation::query()->with([
            'user',
            'comment',
        ])->find($id);
        //弹窗界面

        $this->display('user', "举报人")->value($accusation->user->nick_name);
        $this->display('comment', "留言内容")->value($accusation->comment->content ?? "留言已删除");
        $this->radio('flag', '审批')->options([
            '1' => '举报有效，并删除相关留言',
            '2' => '举报无效，驳回举报',
        ])->required();
        $this->hidden('id')->value($id);
        if ($accusation->status != AccusationStatus::created) {
            $this->disableSubmitButton();
        }
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        //设置默认值
        return [
//            'opinion' => '属实',
        ];
    }
}
