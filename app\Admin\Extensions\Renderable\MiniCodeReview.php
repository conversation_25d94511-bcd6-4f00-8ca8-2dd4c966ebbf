<?php


namespace App\Admin\Extensions\Renderable;

use App\Http\Services\impls\WeChatServiceImpl;
use Dcat\Admin\Show;
use Dcat\Admin\Support\LazyRenderable;

class MiniCodeReview extends LazyRenderable
{

    /**
     * 渲染组件.
     *
     * @return mixed
     */
    public function render()
    {
        $data           = $this->getMiniCode($this->type, $this->id);
        $mini_app_image = $data['image_path'];
        $mini_app_path  = $data['mini_app_path'];
        return Show::make(1, ['mini_app_image' => $mini_app_image, 'mini_app_path' => $mini_app_path], function (Show $show) {
            $show->field("mini_app_path", '小程序路径');
            $show->field("mini_app_image", '小程序码')->image();
            $show->disableEditButton();
            $show->disableListButton();
            $show->disableDeleteButton();
        });
    }

    public function getMiniCode($type, $id)
    {
        $path      = "";
        $region    = null;
        $place     = null;
        $tourRoute = null;
        $name      = "";
        if ($type == "region") {
            $path   = "pages/map/map_index";
            $region = \App\Models\Region::query()->find($id);
            $name   = $region->name ?? "";
        } else if ($type == "place") {
            $path  = "pages/user/placeDetail";
            $place = \App\Models\Place::query()->find($id);
            $name  = $place->name ?? "";
        } else if ($type == "tour_route") {
            $path      = "pages/route/route_index";
            $tourRoute = \App\Models\TourRoute::query()->find($id);
            $name      = $tourRoute->name ?? "";
        }

        $query         = [
            'path'  => $path,
            'scene' => "id={$id}",
            'width' => 600,
        ];
        $weChatService = new WeChatServiceImpl();
        $res           = $weChatService->getUnLimitQrcode($query);

        if (isset($res['errcode']) && $res['errcode'] != 0) {
            return "生成小程序码发生错误：错误代码" . $res['errcode'];
        } else {
            $save_path = $res['path'];
        }

        if ($type == "region") {
            if ($region) {
                try {
                    unlink(public_path("storage/" . $region->mini_app_image));
                } catch (\Exception $exception) {

                }
                $region->update([
                    'mini_app_image' => $save_path,
                ]);
            }
        } else if ($type == "place") {
            if ($place) {
                try {
                    unlink(public_path("storage/" . $place->mini_app_image));
                } catch (\Exception $exception) {

                }
                $place->update([
                    'mini_app_image' => $save_path,
                ]);
            }
        } else if ($type == "tour_route") {
            if ($tourRoute) {
                try {
                    unlink(public_path("storage/" . $tourRoute->mini_app_image));
                } catch (\Exception $exception) {

                }
                $tourRoute->update([
                    'mini_app_image' => $save_path,
                ]);
            }
        }
        return [
            'image_path'    => $save_path,
            'mini_app_path' => $path . "?id=" . $id
        ];
    }
}
