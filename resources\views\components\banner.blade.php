@props(['banners' => [], 'type' => 'main'])

@if($type === 'main')
<!-- 主轮播图 -->
<div class="banner">
    <div data-am-widget="slider" class="am-slider am-slider-a2" data-am-slider="{'directionNav':false,'slideshowSpeed': 3000}">
        <ul class="am-slides">
            @forelse($banners as $banner)
                <li>
                    <div class="ddsgg" style="background: url({{ asset($banner['image']) }}) no-repeat center;">
                        <div class="jz">
                            <div class="bn_bt">{{ $banner['title'] }}</div>
                            @if(!empty($banner['subtitle']))
                                <div class="bn_subtitle">{{ $banner['subtitle'] }}</div>
                            @endif
                        </div>
                    </div>
                </li>
            @empty
                <li>
                    <div class="ddsgg" style="background: url({{ asset('static/images/1-200q3113p40-l.jpg') }}) no-repeat center;">
                        <div class="jz">
                            <div class="bn_bt">指向创造力的卓越教师培养模式探索与实践</div>
                        </div>
                    </div>
                </li>
            @endforelse
        </ul>
    </div>
</div>
@else
<!-- 副轮播图 -->
<div class="sbanner">
    <div data-am-widget="slider" class="am-slider am-slider-b1" data-am-slider="{'controlNav':false,'slideshowSpeed': 3000}">
        <ul class="am-slides">
            @forelse($banners as $banner)
                <li>
                    <div class="ddsgg">
                        @if(!empty($banner['link']))
                            <a href="{{ $banner['link'] }}" target="{{ $banner['target'] ?? '_self' }}">
                                <img src="{{ asset($banner['image']) }}" width="100%" alt="{{ $banner['title'] }}" />
                            </a>
                        @else
                            <img src="{{ asset($banner['image']) }}" width="100%" alt="{{ $banner['title'] }}" />
                        @endif
                    </div>
                </li>
            @empty
                <li>
                    <div class="ddsgg">
                        <img src="{{ asset('static/picture/1-200q31343370-l.jpg') }}" width="100%" />
                    </div>
                </li>
            @endforelse
        </ul>
    </div>
</div>
@endif
