<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateArticlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title')->nullable()->comment('标题');
            $table->string('alias')->nullable()->comment('别名');
            $table->string('url')->nullable()->comment('链接');
            $table->string('keywords')->nullable()->comment('关键词');
            $table->string('description')->nullable()->comment('描述');
            $table->string('image')->nullable()->comment('缩略图');
            $table->longText('content')->nullable()->comment('内容');
            $table->longText('extra')->nullable()->comment('附加字段');
            $table->string('model')->nullable()->comment('模板');
            $table->integer('recommend')->default('0')->nullable()->comment('是否推荐');
            $table->integer('order')->default('99')->nullable()->comment('序号');
            $table->integer('show')->default('1')->nullable()->comment("是否显示");
            $table->integer('top')->default('0')->nullable()->comment("是否置顶");
            $table->unsignedBigInteger('view')->default('0')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('articles');
    }
}
