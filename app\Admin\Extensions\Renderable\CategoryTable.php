<?php


namespace App\Admin\Extensions\Renderable;


use App\Admin\Repositories\Category;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class CategoryTable extends LazyRenderable
{

    public function grid(): Grid
    {
        $model = $this->model;

        return Grid::make((new Category())->model()->where("model", $model)->whereNull("deleted_at"), function (Grid $grid) {
            $grid->column('id');
//            $grid->level->tree();
            $grid->column('name');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->paginate(10);
            $grid->disableActions();


            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('name')->width(4);
            });
        });
    }
}
