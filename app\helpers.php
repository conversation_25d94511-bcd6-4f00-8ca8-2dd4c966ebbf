<?php

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

if (!function_exists('user_admin_config')) {
    function user_admin_config($key = null, $value = null)
    {
        $folder = storage_path("app/config/");
        $path   = $folder . "backend.json";

        if (!file_exists($folder)) {
            mkdir($folder, 0777, true);
        }

        if (file_exists($path)) {
            $config = file_get_contents($path);
            $config = json_decode($config, true);
        } else {
            $config = [];
        }

        if (is_array($key)) {
            // 保存
            foreach ($key as $k => $v) {
                Arr::set($config, $k, $v);
            }
            file_put_contents($path, json_encode($config));
            return;
        }

        if ($key === null) {
            return $config;
        }

        return Arr::get($config, $key, $value);
    }
}

if (!function_exists('user_front_config')) {
    function user_front_config($key = null, $value = "")
    {
        $folder = storage_path("app/config/");
        $path   = $folder . "front.json";

        if (!file_exists($folder)) {
            mkdir($folder, 0777, true);
        }

        if (file_exists($path)) {
            $config = file_get_contents($path);
            $config = json_decode($config, true);
        } else {
            $config = [];
        }

        if (is_array($key)) {
            // 保存
            foreach ($key as $k => $v) {
                Arr::set($config, $k, $v);
            }

            file_put_contents($path, json_encode($config));

            return;
        }

        if ($key === null) {
            return $config;
        }

        return Arr::get($config, $key, $value);
    }
}
if (!function_exists('user_front_image')) {
    function user_front_image($path)
    {
        if (Str::startsWith($path, ['http', 'https'])) {
            return $path;
        }
        return asset("storage" . "/" . $path);
    }
}


/**
 * 返回操作成功json
 * @param string $msg
 * @param array $data
 * @return array
 */
if (!function_exists("success")) {
    function success($data = [], $msg = 'success')
    {
        return response()->json([
            "code"    => 200,
            "massage" => $msg,
            "data"    => $data
        ]);
    }
}
/**
 * 输出错误信息
 * @param int $code
 * @param $msg
 */
if (!function_exists("error")) {
    function error($msg, $code = 0)
    {
        return response()->json([
            'code'  => $code,
            'error' => $msg
        ]);
    }
}


if (!function_exists("navigation_url")) {
    function navigation_url($url)
    {
        if (\Illuminate\Support\Str::startsWith($url, [
            'http',
            'https',
        ])) {
            return $url;
        } elseif (\Illuminate\Support\Str::startsWith($url, '/')) {
            return url($url);
        } elseif ($url == "" || $url == "#") {
            return 'javascript:void(0);';
        } else {

            return "/" . $url;
        }
    }
}

if (!function_exists('view_static')) {

    function view_static($html = "-", $path = "-")
    {
        Storage::disk('local')->put('views/' . $path, $html);
    }
}

if (!function_exists('view_static_del')) {

    function view_static_del()
    {
        $view_path = storage_path('app/views/');
        deldir($view_path);
    }
}

if (!function_exists('view_static_make')) {

    function view_static_make()
    {
        view_static_del();
        $all_url          = [env('APP_URL')];
        $VIEW_STATIC_URLS = explode(",", env('VIEW_STATIC_URL'));
        for ($i = 0; $i < count($all_url); $i++) {
            $url = $all_url[$i];
            try {
                $html = file_get_contents($url);
            } catch (Exception $exception) {
                continue;
            }
            preg_match_all('/<a(.*?)href="(.*?)"(.*?)>/i', $html, $matches);
            foreach ($matches[2] as $new_url) {
                if (!in_array($new_url, $all_url)) {
                    foreach ($VIEW_STATIC_URLS as $VIEW_STATIC_URL) {
                        if (strpos($new_url, $VIEW_STATIC_URL) === 0) {
                            echo $new_url . " add" . "\r\n";
                            $all_url[] = $new_url;
                        }
                    }
                }
            }
        }
    }
}

if (!function_exists('deldir')) {

    function deldir($path)
    {
        //如果是目录则继续
        if (is_dir($path)) {
            //扫描一个文件夹内的所有文件夹和文件并返回数组
            $p = scandir($path);
            foreach ($p as $val) {
                //排除目录中的.和..
                if ($val != "." && $val != "..") {
                    //如果是目录则递归子目录，继续操作
                    if (is_dir($path . $val)) {
                        //子目录中操作删除文件夹和文件
                        deldir($path . $val . '/');
                        //目录清空后删除空文件夹
                        @rmdir($path . $val . '/');
                    } else {
                        //如果是文件直接删除
                        unlink($path . $val);
                    }
                }
            }
        }
    }
}

if (!function_exists('get_base64_encode')) {
    function get_base64_encode($str)
    {
        return \Illuminate\Support\Str::replace("=", "", base64_encode($str));
    }
}


if (!function_exists("get_mini_app_config")) {
    function get_mini_app_config()
    {
        return [
            "app_id"        => env("MINI_APP_ID"),
            "secret"        => env("MINI_APP_SECRET"),
            "response_type" => "array",
            "log"           => [
                'level' => 'debug',
                'file'  => storage_path('logs/wechat.log'),
            ],
        ];
    }
}

if (!function_exists("get_pay_config")) {
    function get_pay_config()
    {
        return [
            "app_id"        => env("MINI_APP_ID"),
            "mch_id"        => env("MCH_ID"),
            "key"           => env("MCH_KEY"),
            "response_type" => "array",
            "log"           => [
                'level' => 'debug',
                'file'  => storage_path('logs/wechat.log'),
            ],
        ];
    }
}


if (!function_exists("get_public_app_config")) {
    function get_public_app_config()
    {
        return [
            "app_id"        => env("PUBLIC_APP_ID"),
            "secret"        => env("PUBLIC_APP_SECRET"),
            "response_type" => "array",
            "log"           => [
                'level' => 'debug',
                'file'  => storage_path('logs/wechat.log'),
            ],
        ];
    }
}


if (!function_exists("get_uuid")) {
    function get_uuid($len = 0)
    {
        $int = '';

        while (strlen($int) != $len) {
            $int .= mt_rand(0, 9);
        }

        return date('Ymd') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8) . $int;
    }
}


if (!function_exists("get_random_unique_element")) {
    function get_random_unique_element($arr, $num = 1)
    {
        $arr_unique = array_unique($arr);
        if (count($arr_unique) <= $num) {
            return $arr_unique;
        }
        $selected = [];
        while (count($selected) < $num) {
            $choice = $arr[array_rand($arr, 1)];
            if (!in_array($choice, $selected)) {
                $selected[] = $choice;
            }
        }
        return $selected;
    }
}

if (!function_exists("getFileType")) {
    function getFileType($file)
    {
        $type = 0;
        $res  = false;
        //20201230 安全校验
        $fp  = fopen($file, 'rb');
        $bin = fread($fp, 2); //利用PHP取二进制文件头判断文件类型,每个文件在产生的时候,他是什么类型的文件,都由这个二进制头部的2个字节决定的
        fclose($fp);
        if ($bin == '') {
            $type = 0;
        } else {
            $strInfo  = unpack("C2chars", $bin); // 函数从二进制字符串对数据进行解包。
            $typeCode = intval($strInfo['chars1'] . $strInfo['chars2']);
            $type     = $typeCode;
        }
        return $type;
    }
}

/**
 * 上传文件带裁切
 */
if (!function_exists("upload_file_single")) {
    function upload_file_single($file, $code = '')
    {
        try {
            // if ($file->isValid()) {
            //获取文件基本信息
            $extension   = $file->getClientOriginalExtension();
            $filename    = $file->getClientOriginalName();
            $path        = $file->getRealPath(); //路径
            $newFileName = "upload/" . Str::random(40) . "." . $extension;

            //增加校验图片是否含有违规内容处理 20211209
            /* if (in_array(strtolower($extension), array('png', 'jpg', 'jpeg', 'gif'))) {
                if (!imgSecCheck($file)) {
                    return error("您上传的图片包含敏感信息！", 40001);
                }
            } */

            $disk = Storage::disk('public');

            $disk->put($newFileName, file_get_contents($path));

            $width  = '';
            $height = '';

            //\Log::info(strtolower($extension));
            if (strtolower($extension) == 'jpg' || strtolower($extension) == 'png') {
                if (strtolower($extension) == 'jpg') {
                    $img = imagecreatefromjpeg($path);
                } else {
                    $img = imagecreatefrompng($path);
                }

                $width  = imagesx($img);
                $height = imagesy($img);
            }

            //将存储后的信息存入数据库
            /* $attchment = SystemAttachment::create([
                    "filename" => $filename,
                    "filesize" => $disk->getSize($newFileName),
                    "fileurl" => $newFileName,
                    "status" => 1,
                    "width" => $width,
                    "height" => $height
                ]); */
            $data             = [];
            $data["file_url"] = $newFileName;

            /*  $data = [];
                $data["file"] = resultData($attchment->id, $newFileName); //默认的返回上传的原图数据
                $data["file"]["file_name"] =  $filename; */

            return $data;
            /*   }else{
                dd(66);
            } */
        } catch (\Exception $e) {
            return error($e->getMessage());
            //systemLog('上传图片', $e->getMessage());
        }
    }
}

if (!function_exists("get_config")) {
    function get_config($key, $default = "")
    {
        $config = json_decode(file_get_contents(storage_path("app/config/front.json")), true);
        if (!isset($config[$key]) || $config[$key] == null || $config[$key] == "") {
            return $default;
        }
        return $config[$key];
    }
}

if (!function_exists("emoji_filter")) {
    function emoji_filter($str)
    {
        $str = preg_replace_callback(
            '/./u',
            function (array $match) {
                return strlen($match[0]) >= 4 ? '' : $match[0];
            },
            $str);

        return $str;
    }
}


if (!function_exists("get_lottery_pay_money")) {
    function get_lottery_pay_money($money)
    {
        $money *= 100;
        $money *= 1.02;
        $money = ceil($money);
        return $money / 100;
    }
}


if (!function_exists("get_page_size")) {
    function get_page_size($default_size = 10)
    {
        $request = \Illuminate\Http\Request::capture();
        return $request->input("page_size", $default_size);
    }
}

if (!function_exists("splicing_pdf_plugin_url")) {
    function splicing_pdf_plugin_url($pdf_file_url)
    {
        $plugin_url = env("APP_URL");
        $pdf_plugin = "<iframe class='pdf_iframe' src='{$plugin_url}/pdfjs-2.16.105-legacy/web/viewer.html?file={$pdf_file_url}' style='width: 80%;min-height: 800px'></iframe>";
        return $pdf_plugin;
    }
}

if (!function_exists("handle_content_pdf")) {
    function handle_content_pdf($content)
    {
        $pattern    = "/<a(.*?)href=\"(.*?\.pdf)\"(.*?)>(.*?)<\/a>/";
        $plugin_url = env("APP_URL");
        $content    = preg_replace_callback($pattern, function ($matches) use ($plugin_url) {
            // 解析PDF链接
            $pdf_url = $matches[2];

            // 判断是否为本站链接
            // 如果是本站链接，则替换为iframe显示
            if (Str::startsWith($pdf_url, [env("APP_URL"), env('ALIYUN_OSS_HOST')])) {
                return "<iframe class='pdf_iframe' src='{$plugin_url}/pdfjs-2.16.105-legacy/web/viewer.html?file=" . urlencode($pdf_url) . "' style='width: 100%;min-height: 1000px'></iframe>";
            } else {
                // 如果不是本站链接，则保持原样
                return $matches[0];
            }
        }, $content);

        return $content;
    }
}

if (!function_exists('get_cat_all_article_ids')) {
    function get_cat_all_article_ids($cat_id)
    {
        $sub_cate    = \App\Models\Category::query()->where("parent_id", $cat_id)->pluck("id")->toArray();
        $sub_cate[]  = $cat_id;
        $article_ids = \App\Models\ArticleCategory::query()->whereIn("category_id", $sub_cate)->pluck('article_id')->toArray();
        return $article_ids;
    }
}


if (!function_exists("judge_show_single_pdf")) {
    function judge_show_single_pdf($content)
    {
        $pattern    = "/<a(.*?)href=\"(.*?\.pdf)\"(.*?)>(.*?)<\/a>/";
        $plugin_url = env("APP_URL");
        $matches    = null;
        preg_match_all($pattern, $content, $matches);
        if (isset($matches[2]) && count($matches[2]) == 1) {
            return "{$plugin_url}/pdfjs-2.16.105-legacy/web/viewer.html?file=" . $matches[2][0];
        }
        return null;
    }
}
