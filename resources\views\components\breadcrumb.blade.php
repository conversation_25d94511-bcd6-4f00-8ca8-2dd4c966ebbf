@props(['title' => '', 'breadcrumbs' => []])

<div class="bannern c">
    <div class="jz c">
        <div class="nbnbtz">指向创造力的卓越教师培养模式探索与实践</div>
    </div>
</div>

<div class="n_cont c">
    <div class="jz c">
        <div class="ny_nav" id="my_sticky">
            <div class="wjssbt c">{{ $title }}</div>
            <div class="wjssbtt c">
                <img src="{{ asset('static/picture/cot.jpg') }}" 
                     width="252" height="106" alt="{{ $title }}" />
            </div>
            <ul class="wjsndh c">
                <li class="c">
                    <a href="{{ route('home') }}" target=""> 首页 <span> > </span> </a>
                </li>
                @foreach($breadcrumbs as $breadcrumb)
                    <li class="c {{ $loop->last ? 'on' : '' }}">
                        @if($loop->last)
                            <span>{{ $breadcrumb['title'] }}</span>
                        @else
                            <a href="{{ $breadcrumb['url'] }}" target=""> 
                                {{ $breadcrumb['title'] }} <span> > </span> 
                            </a>
                        @endif
                    </li>
                @endforeach
            </ul>
        </div>
        
        <script>
            $(window).bind("scroll", function () {
                var div_h = $(".ny_nav").height(); // div的高度
                var foot_h = $(".foot").offset().top - $(window).scrollTop(); // foot到顶部的距离
                var con_h = $(".ny_zqw_chb").offset().top - $(window).scrollTop(); // con到顶部的距离
                if (con_h <= 0) {
                    $("#my_sticky").addClass("ny_nav_chb");
                    if (foot_h - div_h <= 0) {
                        $("#my_sticky").css({
                            top: "auto",
                            bottom: $(window).height() - foot_h,
                        });
                    } else {
                        $("#my_sticky").css({
                            top: "0",
                            bottom: "auto",
                        });
                    }
                } else {
                    $("#my_sticky").removeClass("ny_nav_chb");
                }
            });
        </script>
        
        <div class="ny_zqw ny_zqw_chb">
            <div class="nw_top">
                <span class="top_tzbt l">{{ $title }}</span>
                <span class="top_mbx r">
                    您的位置：<a href="{{ route('home') }}">首页</a>
                    @foreach($breadcrumbs as $breadcrumb)
                        >>
                        @if($loop->last)
                            <a href="javascript:;">{{ $breadcrumb['title'] }}</a>
                        @else
                            <a href="{{ $breadcrumb['url'] }}">{{ $breadcrumb['title'] }}</a>
                        @endif
                    @endforeach
                </span>
            </div>
            
            {{ $slot }}
        </div>
    </div>
</div>
