<?php


namespace App\Http\Controllers;


use App\Models\Article;
use App\Models\Category;
use App\Models\Nav;
use Illuminate\Http\Request;

class HomeController
{
    public function index(Request $request, $alias = null)
    {
        if ($alias != null) {
            $nav   = Nav::query()->where("show", 1)->where("uri", $alias)->first();
            $f_nav = $nav;
            while ($f_nav && $f_nav->parent) {
                $f_nav = $f_nav->parent;
            }
            if (Category::query()->where("show", 1)->where("alias", $alias)->exists()) {
                $cate = Category::query()->where("show", 1)->where("alias", $alias)->first();
                return $this->list($cate->id, $f_nav, $nav);
            }
        }
        return view("pages.home");
    }

    public function list($cate_id, $f_nav = null, $c_nav = null)
    {
        $article_id = \request()->input("article_id", null);
        $cate       = Category::query()->findOrFail($cate_id);

        if (!$f_nav && Nav::query()->where("uri", $cate->alias)->where("parent_id", 0)->exists()) {
            if ($article_id) {
                return redirect()->to($cate->alias . "?article_id=" . $article_id);
            } else {
                return redirect()->to($cate->alias);
            }
        }

        if ($c_nav) {
            if ($c_nav->parent_id == 0) {
                $children = $c_nav->child()->where("show", 1)->orderBy("order")->get();
                if (count($children)) {
                    if ($article_id) {
                        return redirect($children[0]->uri . "?article_id=" . $article_id);
                    } else {
                        return redirect($children[0]->uri);
                    }
                }
            }
        }

        $article = Article::query()->where("show", 1)->find(\request()->input("article_id"));

        $navs = $this->getNavList($c_nav);
        if ($article) {
            if ($article->url) {
                return redirect($article->url);
            }
        }
        if ($article) {
            if (\request()->input('show_pdf_check')) {
                $pdf_url = judge_show_single_pdf($article->content);
                if ($pdf_url) {
                    return redirect($pdf_url);
                }
            }
        }
        return view("list", compact('f_nav', 'c_nav', 'navs', 'cate', 'article'));
    }

    public function getNavList($nav)
    {
        $list = [];

        $f_nav = $nav ?? null;
        while ($f_nav) {
            array_unshift($list, [
                "name" => $f_nav->title,
                "uri"  => $f_nav->uri,
            ]);
            $f_nav = $f_nav->parent;
        }
        return $list;
    }

//    public function article($id)
//    {
//        $builder = Article::query();
//
//        $item = $builder->findOrFail($id);
//        $item->increment("view");
//        return view("detail", compact('item'));
//    }

//if (Article::query()->where("show", 1)->where("alias", $alias)->exists()) {
//$article = Article::query()->where("show", 1)->where("alias", $alias)->first();
//return $this->article($article->id);
//} elseif (Category::query()->where("show", 1)->where("alias", $alias)->exists()) {
//$cate = Category::query()->where("show", 1)->where("alias", $alias)->first();
//return $this->list($cate->id);
//}
}
