<?php

use Dcat\Admin\Admin;
use Dcat\Admin\Form\Field\Editor;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Grid\Filter;
use Dcat\Admin\Layout\Navbar;
use Dcat\Admin\Show;
use Dcat\Admin\Support\JavaScript;

/**
 * Dcat-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 *
 * extend custom field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Column::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Filter::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

$userAdminConfig                   = user_admin_config();
$userAdminConfig['upload']['disk'] = 'oss';
$adminConfig                       = array_merge(config('admin'), $userAdminConfig);

if (isset($adminConfig['logo_image']) && $adminConfig['logo_title']) {
    $logoImage                = $adminConfig['logo_image'];
    $logoTitle                = $adminConfig['logo_title'];
    $adminConfig['logo']      = "<img src='{$logoImage}' width='35'> &nbsp;{$logoTitle}";
    $adminConfig['logo-mini'] = "<img src='{$logoImage}' width='35'>";
}

config(['admin' => $adminConfig]);

Admin::navbar(function (Navbar $navbar) {

    $method = config('admin.layout.horizontal_menu') ? 'left' : 'right';

    $setting_front_url = route("dcat.admin.setting.front");
    $setting_admin_url = route("dcat.admin.setting.admin");
    $navbar->$method(<<<HTML
<ul class="nav navbar-nav">
    <li class="nav-item">
        &nbsp;
        <a style="cursor: pointer" href="{$setting_front_url}">
            <i class="feather icon-edit" style="font-size: 1.5rem"></i> 网站设置
        </a>
        &nbsp; &nbsp;
    </li>
    <li class="nav-item">
        <a style="cursor: pointer" href="{$setting_admin_url}">
            <i class="feather icon-edit" style="font-size: 1.5rem"></i> 后台设置
        </a>
    </li>
</ul>

HTML
    );
});

Editor::resolving(function (Editor $editor) {
    $csrf_token = csrf_token();

    // 设置默认配置
    $editor->options([
        'plugins'              => [
            'advlist',
            'autolink',
            'link',
            'image',
            'media',
            'lists',
            'preview',
            'code',
            'help',
            'fullscreen',
            'table',
            'autoresize',
            'codesample',
        ],
        'toolbar'              => [
            'undo redo | preview fullscreen | styleselect | fontsizeselect bold italic underline strikethrough forecolor backcolor | link image media blockquote removeformat codesample',
            'alignleft aligncenter alignright  alignjustify| indent outdent bullist numlist table subscript superscript | code',
        ],
        'min_height'           => 400,
        'save_enablewhendirty' => true,
        'convert_urls'         => false,
        "file_picker_callback" => JavaScript::make(
            <<<JS
function c(callback, value, meta) {
        //文件分类
        var filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4, .jpg, .jpeg, .png';
        //后端接收上传文件的地址
        var upurl = '/budadmin/dcat-api/tinymce/upload?_token={$csrf_token}&dir=tinymce%2Ffiles';
        //为不同插件指定文件类型及后端地址
        //模拟出一个input用于添加本地文件
        var input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('accept', filetype);
        input.click();
        input.onchange = function () {
            var file = this.files[0];

            var xhr, formData;
            console.log(file.name);
            xhr = new XMLHttpRequest();
            xhr.withCredentials = false;
            xhr.open('POST', upurl);
            xhr.onload = function () {
                var json;
                if (xhr.status !== 200) {
                    failure('HTTP Error: ' + xhr.status);
                    return;
                }
                json = JSON.parse(xhr.responseText);
                if (!json || typeof json.location != 'string') {
                    failure('Invalid JSON: ' + xhr.responseText);
                    return;
                }
                callback(json.location,{title:file.name});
            };
            formData = new FormData();
            formData.append('file', file, file.name);
            xhr.send(formData);
        }
    }
JS
        ),
    ]);
});
