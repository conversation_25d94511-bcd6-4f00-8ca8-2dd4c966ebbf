<!-- 顶部信息栏 -->
<div class="top">
    <div class="jz">
        <div class="tl l">
            欢迎进入东北师范大学2022年申报国家级教学成果奖成果项目网站！
        </div>
        <div class="tr r" id="clock"></div>
    </div>
</div>

<!-- 头部Logo -->
<div class="head">
    <div class="jz">
        <a href="" class="l p_sj">
            <img src="{{ asset('static/picture/1638613778980174.png') }}"
                 alt="东北师范大学2022年申报国家级教学成果奖成果项目" />
        </a>
    </div>
</div>

<!-- 主导航 -->
<div class="nav">
    <div class="jz">
        <ul class="nav_main">
            @foreach($menuItems ?? [] as $item)
                <li class="yiji_li">
                    @if($item['type'] == 'external')
                        <a class="wh_wbd" href="{{ $item['url'] }}" target="_blank">
                            {{ $item['title'] }}
                        </a>
                    @else
                        <a class="wh_wbd" href="{{ $item['url'] }}">
                            {{ $item['title'] }}
                        </a>
                    @endif

                    @if(isset($item['children']) && count($item['children']) > 0)
                        <ul class="nav_c">
                            @foreach($item['children'] as $child)
                                <li class="erji_li c">
                                    <a href="{{ $child['url'] }}">{{ $child['title'] }}</a>
                                </li>
                            @endforeach
                        </ul>
                    @endif
                </li>
            @endforeach
        </ul>
    </div>
</div>

<!-- 移动端导航 -->
<nav data-am-widget="menu" class="am-menu am-menu-dropdown1" data-am-menu-collapse="">
    <a href="javascript: void(0)" class="am-menu-toggle">
        <img src="{{ asset('static/picture/ddh.png') }}" alt="Menu Toggle" />
    </a>
    <ul class="am-menu-nav am-avg-sm-1 am-collapse">
        @foreach($menuItems ?? [] as $item)
            <li class="am-parent">
                @if($item['type'] == 'external')
                    <a href="{{ $item['url'] }}" target="_blank">{{ $item['title'] }}</a>
                @else
                    <a href="{{ $item['url'] }}">{{ $item['title'] }}</a>
                @endif

                @if(isset($item['children']) && count($item['children']) > 0)
                    <ul class="am-menu-sub am-collapse am-avg-sm-2">
                        @foreach($item['children'] as $child)
                            <li class="erji_li c">
                                <a href="{{ $child['url'] }}">{{ $child['title'] }}</a>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </li>
        @endforeach
    </ul>
</nav>
