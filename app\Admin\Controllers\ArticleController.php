<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\CategoryTable;
use App\Admin\Repositories\Article;
use App\Models\ArticleCategory;
use App\Models\Category;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ArticleController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Article(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('title')->textarea();
            $grid->column('alias');
            $grid->column('categories')->display(function ($cate) {
                return implode(",", array_column($cate->toArray(), "name"));
            });
            $grid->column('image')->image(config('filesystems.default'), 50, 50);
            $grid->column('order')->editable();
            $grid->column('show')->switch();
            $grid->column('top')->switch();
            $grid->column('view');
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->model()->orderBy('created_at', 'desc');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('title');
                $filter->where("id", function ($query) {
                    $category_ids   = Category::query()->where("parent_id", $this->input)->pluck("id")->toArray();
                    $category_ids[] = $this->input;
                    $query->whereIn('id', ArticleCategory::query()->whereIn("category_id", $category_ids)->pluck("article_id")->toArray());
                }, "分类")->select(
                    \App\Models\Category::query()->pluck("name", 'id')->toArray()
                );
            });
            $grid->showColumnSelector();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Article(), function (Show $show) {
            $show->field('id');
            $show->field('title');
            $show->field('alias');
            $show->field('keywords');
            $show->field('description');
            $show->field('image')->image();
            $show->content()->unescape();
            $show->field('order');
            $show->field('show')->using([
                1 => '显示',
                0 => '隐藏',
            ]);
            $show->field('top')->using([
                1 => '已置顶',
                0 => '未置顶',
            ]);
            $show->field('view');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $repository = Article::with(['categories']);
        return Form::make($repository, function (Form $form) {
            $form->block(8, function (Form\BlockForm $form) {
                $form->title("基本设置");
                $form->showFooter();

                $form->text('title')->required();
                $form->text('alias')->help("有别名的文章才能被添加到菜单上");
                $form->tree('categories')
                    ->nodes(function () {
                        return \App\Models\Category::query()->get();
                    })
                    ->customFormat(function ($v) use ($form) {
                        if (!$v) return [];
                        // 这一步非常重要，需要把数据库中查出来的二维数组转化成一维数组
                        return array_column($v, 'id');
                    });
                $form->editor('content')->required();
//                $form->editor('content')->required()->options([
//                    'plugins'              => [
//                        'advlist',
//                        'autolink',
//                        'link',
//                        'image',
//                        'media',
//                        'lists',
//                        'preview',
//                        'code',
//                        'help',
//                        'fullscreen',
//                        'table',
//                        'autoresize',
//                        'codesample',
//                    ],
//                    'toolbar'              => [
//                        'undo redo | preview fullscreen | styleselect | fontsizeselect bold italic underline strikethrough forecolor backcolor | link image media blockquote removeformat codesample',
//                        'alignleft aligncenter alignright  alignjustify| indent outdent bullist numlist table subscript superscript | code',
//                    ],
//                    'min_height'           => 400,
//                    'save_enablewhendirty' => true,
//                    'convert_urls'         => false,
//                ]);
            });

            $form->block(4, function (Form\BlockForm $form) {

                $form->title("封面图");
                $form->photo('image')
                    ->nametype('datetime')
                    ->remove(true)
                    ->help('推荐分辨率：400*500');


                $form->next(function (Form\BlockForm $form) {
                    $form->title("外部链接");
                    $form->text('url')->default("")->help("输入外部链接后，点击文章进入第三方网站");
                });

                $form->next(function (Form\BlockForm $form) {
                    $form->title("更多功能");
                    $form->switch('show')->default(1);
                    $form->switch('top')->default(0);
                    $form->number('order')->default(99)->help("序号越小越靠前");
                    $form->number('view')->default(0);
                    $form->datetime('created_at')->default(now());
                    $form->display('updated_at');
                });
                $form->hidden('model')->value("article");
            });
        });
    }
}
