<?php


namespace App\Admin\Extensions\Actions;


use App\Admin\Extensions\Renderable\MiniCodeReview;
use App\Admin\Repositories\Place;
use App\Admin\Repositories\Region;
use App\Admin\Repositories\TourRoute;
use App\Http\Services\impls\WeChatServiceImpl;
use Dcat\Admin\Grid\Displayers\Actions;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;

class GetMiniCodeAction extends RowAction
{
    private $params;

    public function __construct($title = null, $params = [])
    {
        $this->params = $params;
        parent::__construct($title);
    }

    /**
     * @return array|null|string
     */
    public function title()
    {
        if ($this->title) {
            return $this->title;
        }
        if (Actions::class == user_admin_config("grid.grid_action_class")) {
            return '<i class="fa fa-qrcode" title="生成小程序码"></i> ' . ' &nbsp;&nbsp;';
        }
        return '<i class="fa fa-qrcode"></i> ' . "生成小程序码" . ' &nbsp;&nbsp;';
    }


    public function render()
    {
        $type = $this->params['type'];
        $id   = $this->getKey();
        return Modal::make()
            ->xl()
            ->title($this->title())
            ->body(MiniCodeReview::make([
                'id'   => $id,
                'type' => $type
            ]))
            ->button($this->title());
    }


}
