<?php

namespace App\Jobs;

use App\Http\Services\LotteryService;
use App\Models\Lottery;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessLotteryOpen implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $lottery;

    /**
     * Create a new job instance.
     *
     * @param $lottery
     */
    public function __construct(Lottery $lottery)
    {
        $this->lottery = $lottery->withoutRelations();
    }

    /**
     * Execute the job.
     *
     * @param LotteryService $lotteryService
     * @return void
     */
    public function handle(LotteryService $lotteryService)
    {
        $lotteryService->lotteryOpen($this->lottery->id);
    }
}
