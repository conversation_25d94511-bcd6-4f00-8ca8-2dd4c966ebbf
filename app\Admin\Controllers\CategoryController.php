<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\CategoryTable;
use App\Admin\Repositories\Category;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CategoryController extends AdminController
{

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Category(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name')->editable();
            $grid->column('parent_id')->display(function ($parent_id) {
                return \App\Models\Category::query()->find($parent_id)->name ?? "无";
            });
            $grid->column('image')->image(config('filesystems.default'), 50, 50);
            $grid->column('alias')->editable();
            $grid->column('page_size')->editable();
            $grid->column('show')->switch();
            $grid->column('order')->editable();
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('name');
                $filter->equal('parent_id')->select(
                    \App\Models\Category::query()->pluck("name", 'id')->toArray()
                );
            });
            $grid->showColumnSelector();

            $grid->model()->orderBy('created_at', 'desc');

        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Category(), function (Show $show) {
            $show->field('id');
            $show->field('parent_id');
            $show->field('name');
            $show->field('page_size');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Category(), function (Form $form) {
            $form->block(8, function (Form\BlockForm $form) {

                // 设置标题
                $form->title('基本设置');


                $form->selectTable('parent_id')
                    ->default(0)
                    ->from(CategoryTable::make(['id' => $form->getKey()]))
                    ->model(\App\Models\Category::class, 'id', 'name');
                $form->text('name')->required();
                $form->text('alias')->required();
                $form->showFooter();

            });

            $form->block(4, function (Form\BlockForm $form) {
                $form->title("封面图");
                $form->photo('image')
                    ->nametype('datetime')
                    ->remove(true)
                    ->help('推荐分辨率：400*500');

                $form->title("更多功能");
                $form->text('page_size')->default(15);

                $form->switch("show")->default(1);
                $form->number("order")->default(99);

                $form->display('created_at');
                $form->display('updated_at');
            });
        });
    }
}
