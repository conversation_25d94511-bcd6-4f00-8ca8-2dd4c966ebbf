<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\Forms\AdminSetting;
use App\Admin\Extensions\Forms\FrontSetting;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;

class SettingController
{
    public function front(Content $content)
    {
        return $content->body(
            Card::make("网站配置", FrontSetting::make())
        );
    }

    public function admin(Content $content)
    {
        return $content->body(
            Card::make("后台配置", AdminSetting::make())
        );
    }

    public function clear(Content $content)
    {
        view_static_del();

        admin_success("清除缓存成功");

        return redirect(admin_url("/"));
    }
}
