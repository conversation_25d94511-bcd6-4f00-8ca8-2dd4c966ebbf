<?php


namespace App\Admin\Extensions\Renderable;


use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use Dcat\Admin\Http\Repositories\Administrator;

class AdminTable extends LazyRenderable
{
    public function grid(): Grid
    {
        $id = $this->id;
        return Grid::make((new Administrator())->model()->where("institution_id", $this->institution_id), function (Grid $grid) {
            $grid->column('id');
            $grid->column('name', "名称");
            $grid->column('username', "手机号");
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', "名称")->width(4);
                $filter->like('username', "手机号")->width(4);
            });
        });
    }
}
