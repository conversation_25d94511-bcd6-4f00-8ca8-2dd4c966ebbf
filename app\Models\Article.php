<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    public function categories(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        $pivotTable = 'article_categories'; // 中间表

        $relatedModel = Category::class; // 关联模型类名

        return $this->belongsToMany($relatedModel, $pivotTable, 'article_id', 'category_id');
    }


    public function getLink($cate)
    {
        return route("front.list", $cate->id) . "?article_id={$this->id}";
    }
}
