<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Dcat\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    use ModelTree;

    protected $parentColumn = 'parent_id';
    protected $orderColumn = 'id';

    public function articles()
    {
        return $this->belongsToMany(Article::class, "article_categories");
    }

    public function child()
    {
        return $this->hasMany(Category::class, "parent_id", "id");
    }

    public function parent()
    {
        return $this->hasMany(Category::class, "id", "parent_id");
    }

    public function getLink()
    {
        return route("front.list",$this->id);
    }
}
