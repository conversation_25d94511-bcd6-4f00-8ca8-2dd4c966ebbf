<?php


namespace App\Admin\Extensions\Forms;

use App\Models\Category;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Arr;

class FrontSetting extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * 处理表单请求.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        foreach (Arr::dot($input) as $k => $v) {
            $this->update($k, $v);
        }
        return $this->response()->success('设置成功');
    }

    /**
     * 构建表单.
     */
    public function form()
    {
//        $this->tab("提现设置", function () {
//            $this->text('withdrawal_min_money', "提现最小金额");
//        });

        $this->tab("头部设置", function () {
            $this->text("title1", "标题");
//            $this->text("title2", "标题2");
//            $this->text("title3", "标题3");
//            $this->select("fished_person", "完成人信息分类")->options(
//                Category::query()->pluck("name", 'id')->toArray()
//            );
        });

        $this->tab("首页设置", function () {
//            $this->video('index_video',"视频")
//                ->nametype('datetime')
//                ->remove(true);

            $this->select("index_top_slider", "顶部轮播列表")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );

            $this->select("article_list_model_1", "文章列表板块1")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_2", "文章列表板块2")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_3", "文章列表板块3")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_4", "文章列表板块4")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_5", "文章列表板块5")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_6", "文章列表板块6")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_7", "文章列表板块7")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_8", "文章列表板块8")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );
            $this->select("article_list_model_9", "文章列表板块9")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );

            $this->select("bottom_link", "底部链接")->options(
                Category::query()->pluck("name", 'id')->toArray()
            );


            $this->divider();
        });
    }

    /**
     * 设置接口保存成功后的回调JS代码.
     *
     * 1.2秒后刷新整个页面.
     *
     * @return string|void
     */
    public function savedScript()
    {
//        return <<<'JS'
//    if (data.status) {
//        setTimeout(function () {
//          location.reload()
//        }, 1200);
//    }
//JS;
    }

    /**
     * 返回表单数据.
     *
     * @return array
     */
    public function default()
    {
        return user_front_config();
    }

    /**
     * 更新配置.
     *
     * @param string $key
     * @param string $value
     */
    protected function update($key, $value)
    {
        user_front_config([$key => $value]);
    }
}
