@props(['tabSections' => []])

<div class="bg-grey">
    <div class="content">
        <div class="tab-block information-tab">
            <div class="tab-buttons">
                @foreach($tabSections as $index => $section)
                    <h3 class="tab-button {{ $index === 0 ? 'cur' : '' }}" data-tab="{{ $section['key'] }}">
                        {{ $section['title'] }}
                        @if($section['key'] === 'three')
                            <span class="question-icon"></span>
                        @endif
                    </h3>
                @endforeach
            </div>
            
            <div class="tabs">
                @foreach($tabSections as $index => $section)
                    <div class="tab-item {{ $index === 0 ? 'active' : '' }}" id="tab-{{ $section['key'] }}">
                        <div class="information-tab">
                            <div class="information-left">
                                @if(!empty($section['more_url']))
                                    <a href="{{ $section['more_url'] }}">
                                        <img src="{{ asset($section['featured_image']) }}" 
                                             width="500" height="340" 
                                             alt="{{ $section['title'] }}" />
                                    </a>
                                @else
                                    <img src="{{ asset($section['featured_image']) }}" 
                                         width="500" height="340" 
                                         alt="{{ $section['title'] }}" />
                                @endif
                                
                                <div class="left-bottom">
                                    <div class="article-title">{{ $section['title'] }}</div>
                                    @if(!empty($section['featured_date']))
                                        <div class="article-time">{{ $section['featured_date'] }}</div>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="information-right">
                                @foreach($section['items'] ?? [] as $itemIndex => $item)
                                    <div class="article-list {{ $itemIndex === 0 ? 'current' : '' }}">
                                        <a href="{{ $item['url'] }}" class="article-link" 
                                           target="{{ $item['target'] ?? '_blank' }}">
                                            <div class="article-head">
                                                <span class="article-number">{{ $itemIndex + 1 }}</span>
                                                <span class="article-title">{{ $item['title'] }}</span>
                                                @if(!empty($item['date']))
                                                    <span class="article-time">{{ $item['date'] }}</span>
                                                @endif
                                            </div>
                                        </a>
                                    </div>
                                @endforeach
                                
                                @if(!empty($section['more_url']))
                                    <div class="more-link">
                                        <a href="{{ $section['more_url'] }}" class="btn-more">查看更多</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script type="text/javascript">
    // Tab切换
    $(".tab-button").click(function () {
        var tab = $(this).data("tab");
        $(this).addClass("cur").siblings(".tab-button").removeClass("cur");
        $("#tab-" + tab + "").addClass("active").siblings(".tab-item").removeClass("active");
    });
    
    // 新闻列表切换
    $(".information-tab .article-list").hover(
        function () {
            $(this).addClass("current").siblings(".article-list").removeClass("current");
        }
    );
</script>
@endpush
