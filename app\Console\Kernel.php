<?php

namespace App\Console;

use App\Http\Services\impls\LotteryServiceImpl;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $lotteryService = new LotteryServiceImpl();

        // $schedule->command('inspire')->hourly();
        $schedule->call(function () use ($lotteryService) {
            $lotteryService->handleCanOpenLottery();
            $lotteryService->handleOpenedLottery();
//            Log::info("------------------计划任务-------------------------------");
        })->everyMinute();

        $schedule->call(function () use ($lotteryService) {
            $lotteryService->handleExpiredLottery();
//            Log::info("------------------处理超期-------------------------------");
        })->everyMinute();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
