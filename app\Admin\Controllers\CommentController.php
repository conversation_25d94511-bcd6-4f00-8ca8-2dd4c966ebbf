<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\ClientUserTable;
use App\Models\ClientUser;
use App\Models\Comment;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CommentController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Comment(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column("user")->display(function ($user) {
                return $user->nick_name ?? "";
            });
            $grid->column('content');
            $grid->column('like');
            $grid->column('top')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->model()->orderByDesc("id");
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal("id", "用户")->selectTable(ClientUserTable::make())->model(ClientUser::class, "id", "nick_name");
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Comment(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('content');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Comment(), function (Form $form) {
            $form->display('id');
            $form->selectTable("user_id", '用户')->title("用户")->from(ClientUserTable::make())->model(ClientUser::class, 'id', 'nick_name')->required();
            $form->textarea('content')->required();
            $form->number('like');
            $form->switch('top');
            $form->display('created_at');
            $form->display('updated_at');
            $form->hidden("top_at");

            $form->saving(function (Form $form) {
                if ($form->model()->top == 0 && $form->top == 1) {
                    $form->top_at = now();
                    $form->updates();
                }
            });
        });
    }
}
