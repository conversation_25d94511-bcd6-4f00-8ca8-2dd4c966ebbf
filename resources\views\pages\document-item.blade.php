@extends('layouts.app')

@section('title', $item->title)
@section('pageType', '2')
@section('pageTitle', $item->title)
@section('keywords', $item->section->page->meta_keywords ?? '')
@section('description', $item->section->page->meta_description ?? '')

@section('content')
<!-- 横幅区域 -->
<div class="bannern c">
    <div class="jz c">
        <div class="nbnbtz">指向创造力的卓越教师培养模式探索与实践</div>
    </div>
</div>

<div class="n_cont c">
    <div class="jz c">
        <div class="ny_nav" id="my_sticky">
            <div class="wjssbt c">{{ $item->section->page->title }}</div>
            @if($item->section->page->featured_image)
            <div class="wjssbtt c">
                <img src="{{ asset($item->section->page->featured_image) }}"
                     width="252" height="106" alt="{{ $item->section->page->title }}" />
            </div>
            @endif
            <ul class="wjsndh c">
                <li class="c on">
                    <a href="{{ route('home') }}" target=""> 首页 <span> > </span> </a>
                </li>
                <li class="c">
                    <a href="{{ route('pages.show', $item->section->page->slug) }}"> {{ $item->section->page->title }} <span> > </span> </a>
                </li>
                <li class="c">
                    <a href="javascript:;"> {{ $item->title }} </a>
                </li>
            </ul>
        </div>

        <div class="ny_zqw ny_zqw_chb">
            <div class="nw_top">
                <span class="top_tzbt l">{{ $item->title }}</span>
                <span class="top_mbx r">
                    您的位置：<a href="{{ route('home') }}">首页</a>>><a href="{{ route('pages.show', $item->section->page->slug) }}">{{ $item->section->page->title }}</a>>><a href="javascript:;">{{ $item->title }}</a>
                </span>
            </div>

            <!-- 文档内容 -->
            <div class="bjbld">
                <div class="xqbt">{{ $item->title }}</div>
                <div class="xqbtnm">
                    <div class="xqbrq">
                        <span class="rqds l">{{ $item->created_at->format('Y-m-d') }}</span>
                    </div>
                </div>

                @if($item->content)
                <div class="xqxq">
                    {!! $item->content !!}
                </div>
                @endif

                @if($item->file_path)
                <div class="document-download" style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
                    <h4>文档下载</h4>
                    <p>
                        <a href="{{ asset($item->file_path) }}" target="_blank" class="download-link">
                            <i class="fa fa-download"></i> 下载文档
                        </a>
                    </p>
                </div>
                @endif

                <!-- 返回列表按钮 -->
                <div class="back-to-list" style="margin-top: 30px; text-align: center;">
                    <a href="{{ route('pages.show', $item->section->page->slug) }}" class="btn btn-primary">
                        返回{{ $item->section->page->title }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(window).bind("scroll", function () {
    var div_h = $(".ny_nav").height(); // div的高度
    var foot_h = $(".foot").offset().top - $(window).scrollTop(); // foot到顶部的距离
    var con_h = $(".ny_zqw_chb").offset().top - $(window).scrollTop(); // con到顶部的距离
    if (con_h <= 0) {
        $("#my_sticky").addClass("ny_nav_chb");
        if (foot_h - div_h <= 0) {
            $("#my_sticky").css({
                top: "auto",
                bottom: $(window).height() - foot_h,
            });
        } else {
            $("#my_sticky").css({
                top: "0",
                bottom: "auto",
            });
        }
    } else {
        $("#my_sticky").removeClass("ny_nav_chb");
    }
});
</script>
@endpush

@push('styles')
<style>
.back-to-list .btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.back-to-list .btn:hover {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
}

.document-download {
    border-left: 4px solid #007bff;
}

.download-link {
    display: inline-block;
    padding: 10px 15px;
    background-color: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.download-link:hover {
    background-color: #218838;
    color: white;
    text-decoration: none;
}

.download-link i {
    margin-right: 5px;
}
</style>
@endpush
