# 定义部署函数
deploy_project() {
    local ip=$1
    local project_name=$2
    local project_path=$3
    local branch_name=$4

    echo ""
    echo -e "\033[34m=====================================================================\033[0m"

    echo "开始更新 $project_name"
    ssh root@$ip "cd $project_path && sshpass -p 'mmx@2020' git pull origin $branch_name"
    ssh root@$ip "cd $project_path && /www/server/php/74/bin/php /usr/bin/composer install"

    # 检查.env文件中debug模式是否打开
    echo "检查$project_name的debug模式..."
    debug_status=$(ssh root@$ip "cd $project_path && grep 'APP_DEBUG' .env | cut -d '=' -f2")
    if [ "$debug_status" = "true" ]; then
        echo -e "\033[31m警告: $project_name的debug模式已打开\033[0m"
    else
        echo -e "\033[32m${project_name}的debug模式已关闭\033[0m"
    fi

    echo -e "\033[34m=====================================================================\033[0m"
    echo ""
}

ip=*************

deploy_project $ip "电工电子-李振梅" "/www/wwwroot/dgdzt.sdut.cloud" "dgdzt"

ip=************

deploy_project $ip "物光" "/www/wwwroot/wgcg.sdut.cloud" "wgcg"

deploy_project $ip "交通" "/www/wwwroot/jtcg.sdut.cloud" "jtcg"

deploy_project $ip "交通2" "/www/wwwroot/jtlx.sdut.cloud" "jtlx"

deploy_project $ip "交通3" "/www/wwwroot/jtyjs.sdut.cloud" "jtyjs"

deploy_project $ip "教务处学生" "/www/wwwroot/jwcxs.sdut.cloud" "jwcxs"

deploy_project $ip "教务处人才" "/www/wwwroot/jwcrc.sdut.cloud" "jwcrc"

echo "更新完成"




